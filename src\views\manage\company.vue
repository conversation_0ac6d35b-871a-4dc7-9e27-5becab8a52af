<template>
    <div class="app-container">
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="单位名称" prop="companyName">
                <el-input v-model="queryParams.companyName" placeholder="请输入单位名称" clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="getList">搜索</el-button>
                <el-button icon="Refresh" @click="reset">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="list">
            <el-table-column label="ID" align="center" prop="id" />
            <el-table-column label="单位名称" align="center" prop="companyName" />
            <el-table-column label="联系人数量" align="center" prop="contactCount" />
            <el-table-column label="属性" align="center" prop="property">
                <template #default="scope">
                    {{ getPropertyLabel(scope.row.property) }}
                </template>
            </el-table-column>
            <el-table-column label="操作" width="280" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
                    <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改单位对话框 -->
        <el-dialog :title="title" v-model="dialogVisible" width="500px" append-to-body @close="handleClose">
            <el-form ref="dataForm" :model="dataForm" :rules="dataRules" label-width="100px">
                <el-form-item label="单位名称" prop="companyName">
                    <el-input v-model="dataForm.companyName" placeholder="请输入单位名称" />
                </el-form-item>
                <el-form-item label="联系人数量" prop="contactCount">
                    <el-input-number v-model="dataForm.contactCount" :min="0" placeholder="请输入联系人数量"
                        style="width: 100%" />
                </el-form-item>
                <el-form-item label="属性" prop="property">
                    <el-select v-model="dataForm.property" placeholder="请选择属性" style="width: 100%">
                        <el-option v-for="item in propertyOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="handleClose">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { getList, addcontacts, updatecontacts, getcontacts, delcontacts } from "@/api/manage/contacts";
export default {
    data() {
        return {
            showSearch: true,
            isChange: false,
            list: [],
            loading: false,
            queryParams: {
                companyName: '',
                pageNum: 1,
                pageSize: 10
            },
            total: 0,
            dialogVisible: false,
            title: '',
            editId: null,
            dataForm: {
                companyName: "",
                contactCount: 0,
                property: ""
            },
            // 属性选项（模拟数据，实际应从后端接口获取）
            propertyOptions: [
                { label: '国有企业', value: 'state_owned' },
                { label: '民营企业', value: 'private' },
                { label: '外资企业', value: 'foreign' },
                { label: '合资企业', value: 'joint_venture' },
                { label: '个体工商户', value: 'individual' },
                { label: '事业单位', value: 'institution' },
                { label: '政府机关', value: 'government' }
            ],
            dataRules: {
                companyName: [
                    { required: true, message: '单位名称不能为空', trigger: 'blur' }
                ],
                contactCount: [
                    { required: true, message: '联系人数量不能为空', trigger: 'blur' },
                    { type: 'number', min: 0, message: '联系人数量不能小于0', trigger: 'blur' }
                ],
                property: [
                    { required: true, message: '属性不能为空', trigger: 'change' }
                ]
            }
        }
    },
    mounted() {
        this.getList()
        this.getPropertyOptions()
    },
    methods: {
        submitForm() {
            this.$refs.dataForm.validate(async (valid) => {
                if (valid) {
                    let dataForm = JSON.parse(JSON.stringify(this.dataForm));

                    if (this.isChange) {
                        dataForm.id = this.editId;
                        let res = await updatecontacts(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '修改成功',
                                type: 'success'
                            });
                            this.getList()
                            this.dialogVisible = false
                        }
                    } else {
                        let res = await addcontacts(dataForm)
                        if (res.code == 200) {
                            this.$message({
                                message: '新增成功',
                                type: 'success'
                            });
                            this.getList()
                            this.dialogVisible = false
                        }
                    }
                } else {
                    return false;
                }
            });
        },
        handleAdd() {
            this.isChange = false;
            this.title = "新增单位";
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs['dataForm'].resetFields();
                this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            })
        },
        handleClose() {
            this.$refs['dataForm'].resetFields();
            this.dataForm = JSON.parse(JSON.stringify(this.$options.data().dataForm))
            this.dialogVisible = false;
        },
        async handleEdit(val) {
            this.title = "修改单位";
            let res = await getcontacts(val.id)
            if (res.code == 200) {
                this.dataForm = res.data;
                this.editId = val.id; // 设置编辑时的ID
            }
            this.dialogVisible = true;
            this.isChange = true;
        },
        handleDelete(val) {
            this.$confirm('确认删除吗？')
                .then(async (_) => {
                    let res = await delcontacts(val.id)
                    if (res.code == 200) {
                        this.$message({
                            message: '删除成功',
                            type: 'success'
                        });
                        this.getList()
                    }
                })
        },
        reset() {
            this.queryParams = {
                companyName: '',
                property: '',
                pageNum: 1,
                pageSize: 10
            };
            this.getList()
        },
        async getList() {
            this.loading = true;
            let res = await getList(this.queryParams)
            if (res.code == 200) {
                this.total = res.total;
                this.list = res.rows;
                this.loading = false;
            }
        },
        // 获取属性选项（模拟接口调用）
        async getPropertyOptions() {
            // 这里应该调用后端接口获取属性选项
            // 暂时使用模拟数据
            // let res = await getPropertyList()
            // if (res.code == 200) {
            //     this.propertyOptions = res.data;
            // }
        },
        // 根据属性值获取属性标签
        getPropertyLabel(value) {
            const option = this.propertyOptions.find(item => item.value === value);
            return option ? option.label : value;
        }
    },
}

</script>
<style lang="scss" scoped>
:deep(.el-dialog) {
    background: #FFFFFF;
    border-radius: 14px;
    position: relative;
}
</style>
