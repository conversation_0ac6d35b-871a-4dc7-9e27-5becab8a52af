import request from '@/utils/request'

// 查询行业列表
export function getIndustryList(query) {
  return request({
    url: '/manage/industry/list',
    method: 'get',
    params: query
  })
}

// 查询行业详细
export function getIndustry(industryId) {
  return request({
    url: '/manage/industry/' + industryId,
    method: 'get'
  })
}

// 新增行业
export function addIndustry(data) {
  return request({
    url: '/manage/industry',
    method: 'post',
    data: data
  })
}

// 修改行业
export function updateIndustry(data) {
  return request({
    url: '/manage/industry',
    method: 'put',
    data: data
  })
}

// 删除行业
export function delIndustry(industryId) {
  return request({
    url: '/manage/industry/' + industryId,
    method: 'delete'
  })
}
