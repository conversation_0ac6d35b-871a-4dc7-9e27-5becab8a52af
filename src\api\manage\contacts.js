import request from '@/utils/request'

// 查询联系人列表
export function getList(query) {
  return request({
    url: '/manage/contacts/list',
    method: 'get',
    params: query
  })
}

// 查询联系人详细
export function getcontacts(contactsId) {
  return request({
    url: '/manage/contacts/' + contactsId,
    method: 'get'
  })
}

// 新增联系人
export function addcontacts(data) {
  return request({
    url: '/manage/contacts',
    method: 'post',
    data: data
  })
}

// 修改联系人
export function updatecontacts(data) {
  return request({
    url: '/manage/contacts',
    method: 'put',
    data: data
  })
}

// 删除联系人
export function delcontacts(contactsId) {
  return request({
    url: '/manage/contacts/' + contactsId,
    method: 'delete'
  })
}
