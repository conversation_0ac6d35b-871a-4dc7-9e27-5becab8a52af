import request from '@/utils/request'

// 查询属地列表
export function getTerritoryList(query) {
  return request({
    url: '/manage/territory/list',
    method: 'get',
    params: query
  })
}

// 查询属地详细
export function getTerritory(territoryId) {
  return request({
    url: '/manage/territory/' + territoryId,
    method: 'get'
  })
}

// 新增属地
export function addTerritory(data) {
  return request({
    url: '/manage/territory',
    method: 'post',
    data: data
  })
}

// 修改属地
export function updateTerritory(data) {
  return request({
    url: '/manage/territory',
    method: 'put',
    data: data
  })
}

// 删除属地
export function delTerritory(territoryId) {
  return request({
    url: '/manage/territory/' + territoryId,
    method: 'delete'
  })
}
