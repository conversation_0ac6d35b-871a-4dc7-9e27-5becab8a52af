import request from '@/utils/request'

// 查询主管部门列表
export function getDepartmentList(query) {
  return request({
    url: '/manage/department/list',
    method: 'get',
    params: query
  })
}

// 查询主管部门详细
export function getDepartment(departmentId) {
  return request({
    url: '/manage/department/' + departmentId,
    method: 'get'
  })
}

// 新增主管部门
export function addDepartment(data) {
  return request({
    url: '/manage/department',
    method: 'post',
    data: data
  })
}

// 修改主管部门
export function updateDepartment(data) {
  return request({
    url: '/manage/department',
    method: 'put',
    data: data
  })
}

// 删除主管部门
export function delDepartment(departmentId) {
  return request({
    url: '/manage/department/' + departmentId,
    method: 'delete'
  })
}
